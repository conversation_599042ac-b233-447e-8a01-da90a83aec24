/* Mobile-First Responsive Styles */

/* Prevent zoom on iOS when focusing inputs */
@media screen and (max-width: 768px) {
  input[type="text"],
  input[type="password"],
  input[type="email"],
  input[type="number"],
  textarea,
  select {
    font-size: 16px !important;
  }
}

/* Custom scrollbar for mobile */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Touch-friendly button styles */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Smooth scrolling for better mobile experience */
html {
  scroll-behavior: smooth;
}

/* Prevent text selection on UI elements */
.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Improve touch manipulation */
.touch-manipulation {
  touch-action: manipulation;
}

/* Mobile-optimized focus styles */
@media (max-width: 768px) {
  button:focus,
  input:focus,
  textarea:focus,
  select:focus {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
  }
}

/* Safe area insets for devices with notches */
@supports (padding: max(0px)) {
  .safe-area-inset-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .safe-area-inset-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
  
  .safe-area-inset-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }
  
  .safe-area-inset-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* Mobile-specific animations */
@media (prefers-reduced-motion: no-preference) {
  .mobile-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  @keyframes slideUp {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
}

/* Responsive text sizes */
.text-responsive-sm {
  font-size: 0.875rem; /* 14px */
}

.text-responsive-base {
  font-size: 1rem; /* 16px */
}

.text-responsive-lg {
  font-size: 1.125rem; /* 18px */
}

@media (min-width: 640px) {
  .text-responsive-sm {
    font-size: 1rem; /* 16px */
  }
  
  .text-responsive-base {
    font-size: 1.125rem; /* 18px */
  }
  
  .text-responsive-lg {
    font-size: 1.25rem; /* 20px */
  }
}

/* Mobile modal improvements */
@media (max-width: 640px) {
  .mobile-modal {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
    overflow-y: auto;
  }
}

/* Improved tap targets for small screens */
@media (max-width: 480px) {
  .mobile-tap-target {
    min-height: 48px;
    min-width: 48px;
    padding: 12px;
  }
}

/* Loading spinner optimizations */
.mobile-spinner {
  width: 2rem;
  height: 2rem;
}

@media (min-width: 640px) {
  .mobile-spinner {
    width: 2.5rem;
    height: 2.5rem;
  }
}

/* Mobile-friendly form spacing */
.mobile-form-spacing > * + * {
  margin-top: 1rem;
}

@media (min-width: 640px) {
  .mobile-form-spacing > * + * {
    margin-top: 1.5rem;
  }
}

/* Responsive grid for mobile */
.mobile-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .mobile-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .mobile-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

/* Mobile-optimized shadows */
.mobile-shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

@media (min-width: 640px) {
  .mobile-shadow {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
}

/* Responsive padding */
.mobile-padding {
  padding: 1rem;
}

@media (min-width: 640px) {
  .mobile-padding {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .mobile-padding {
    padding: 2rem;
  }
}

/* Mobile-friendly border radius */
.mobile-rounded {
  border-radius: 0.75rem; /* 12px */
}

@media (min-width: 640px) {
  .mobile-rounded {
    border-radius: 1rem; /* 16px */
  }
}

/* Accessibility improvements for mobile */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .mobile-high-contrast {
    border: 2px solid;
  }
}

/* Dark mode improvements */
@media (prefers-color-scheme: dark) {
  .mobile-dark-bg {
    background-color: #1f2937;
  }
  
  .mobile-dark-text {
    color: #f9fafb;
  }
  
  .mobile-dark-border {
    border-color: #374151;
  }
}
