# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# Environment Variables & API Keys
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.development
.env.production
.env.test

# API Keys and Secrets (important for translation app)
*.key
*.pem
secrets.json
config/secrets.js
api-keys.json
deepseek-api-key.txt

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.lnk

# Temporary folders
tmp/
temp/

# Local development
.local

# Vercel
.vercel

# Netlify
.netlify

# Package manager files (uncomment if you want to ignore them)
# package-lock.json
# yarn.lock

# Build tools cache
.cache
.parcel-cache
.webpack/

# TypeScript
*.tsbuildinfo

# Serverless directories
.serverless/
