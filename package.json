{"proxy": "http://localhost:3001", "name": "website", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@vercel/blob": "^1.1.1", "@vercel/edge-config": "^1.4.0", "@vercel/speed-insights": "^1.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "openai": "^5.7.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "server": "node server/index.js", "dev": "concurrently \"npm run server\" \"cross-env PORT=3000 npm run start\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}}